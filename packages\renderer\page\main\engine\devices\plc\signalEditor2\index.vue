<template>
  <MyDialog v-model="isActive" title="PLC Digital Signal Editor" width="800px" @ok="onSubmit">
    <div :class="[e('body'), 'cfg-setup']">
      <div :class="['cfg-setup_table', e('body', 'table')]">
        <wui-table
          show-overflow-tooltip
          :data="model.list"
          height="100%"
          border
          class="cfg-setup_table"
          @row-contextmenu="handleRowMenu"
        >
          <wui-table-column prop="name" label="Signal Name" min-width="200px" align="center">
            <template #default="{ row }">
              <wui-input v-if="row.flag" v-model="row.name" clearable placeholder="Please input" />
              <span v-else>{{ row.name }}</span>
            </template>
          </wui-table-column>
          <wui-table-column
            prop="type"
            label="Type"
            min-width="120px"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <wui-select v-if="row.flag" v-model="row.type" placeholder="Select">
                <wui-option
                  v-for="item in TypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </wui-select>
              <template v-else>{{ convertType(TypeOptions, row.type) }}</template>
            </template>
          </wui-table-column>
          <wui-table-column prop="calib_mode" label="Calib Mode" min-width="120px" align="center">
            <template #default="{ row }">
              <wui-select v-if="row.flag" v-model="row.calib_mode" placeholder="Select">
                <wui-option
                  v-for="item in CalibModeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </wui-select>
              <template v-else>{{ convertType(CalibModeOptions, row.calib_mode) }}</template>
            </template>
          </wui-table-column>
          <wui-table-column label="Op" width="100px" align="center">
            <template #default="{ row, $index }">
              <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
            </template>
          </wui-table-column>
          <template #empty>
            <TableTool.Empty @custom-contextmenu="handleTableAreaCtxMenu" />
          </template>
        </wui-table>
      </div>
    </div>
  </MyDialog>
  <signalDialog v-if="signalShow" v-model:model-show="signalShow" :params="signalParam" />
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref, toRef, inject, PropType } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import signalDialog from './signalDialog/index.vue'
import { convertType } from '@/renderer/utils/common'

import TableTool, { OpType } from '@/renderer/components/TableTool'
import { useBem, useBizEngine, usePermanentFocus, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import { useVModel } from '@vueuse/core'
import { PLCContext, SignalParam, PlcSignalTableRow, plcContextKey } from '../type'

const props = defineProps({
  params: {
    type: Object as PropType<SignalParam>,
    default: () => ({})
  },
  modelShow: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelShow'])

const plcContext = inject(plcContextKey)

const signalShow = ref(false)
const signalParam = ref({})

const { e, b } = useBem('plc-signal', $styles)
const TypeOptions = [
  {
    label: 'ADC',
    value: 'ADC'
  },
  {
    label: 'T/C',
    value: 'THM'
  },
  {
    label: 'RTD',
    value: 'RTD'
  }
]
const CalibModeOptions = [
  {
    label: 'None',
    value: 0
  },
  {
    label: 'VerifyOnly',
    value: 1
  },
  {
    label: 'CalibAndVerify',
    value: 2
  }
]
const plcList = ref<any[]>([])
const model = reactive({
  list: [] as PlcSignalTableRow[]
})
const isActive = useVModel(props, 'modelShow', emit)

const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
  toRef(model, 'list'),
  (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'modifyKey':
        handleOp('edit', row, rowIndex)
        break
      case 'signalEditerKey':
        signalShow.value = true
        signalParam.value = {
          currentPlcIndex: props.params.currentPlcIndex,
          plcDeviceIndex: props.params.plcDeviceIndex,
          signalIndex: rowIndex
        }
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'modifyKey', label: 'modify' },
    { key: 'signalEditerKey', label: 'signal editer' }
  ]
)
const handleOp = (op: OpType, row: PlcSignalTableRow, index: number) => {
  switch (op) {
    case 'edit':
      row.flag = true
      break
    case 'cancel':
      handleCancel(row, index)
      break
    case 'select':
      handleSubmit(row, index)
      break
    default:
      break
  }
}
const handleCancel = (row: PlcSignalTableRow, index: number) => {
  const plcSignalItem = plcList.value[index]
  model.list[index] = {
    ...row,
    ...plcSignalItem,
    flag: false
  }
}

const handleSubmit = async (row: PlcSignalTableRow, index: number) => {
  const { flag, row_type, ...params } = row
  const plcSignalItem = plcList.value[index]
  Object.assign(plcSignalItem, params)
  row.flag = false
}

const getDataInfo = async () => {
  const list = plcContext?.plcList.value[props.params.currentPlcIndex]?.devices?.[
    props.params.plcDeviceIndex
  ].signals.list.map(item => ({
    ...item,
    flag: false,
    row_type: '*',
    type: item.type === '' ? 'ADC' : item.type,
    calib_mode: item.calib_mode === -1 ? 0 : item.calib_mode
  })) as PlcSignalTableRow[]

  model.list = plcList.value = list
}

const onSubmit = async () => {
  if (plcContext?.submitSignalData) {
    const signalData = {
      list: model.list.map(item => {
        const { flag, row_type, meta, ...signalItem } = item
        return signalItem
      }),
      input_float: true
    }

    await plcContext.submitSignalData({
      currentPlcIndex: props.params.currentPlcIndex,
      plcDeviceIndex: props.params.plcDeviceIndex,
      signalData
    })
  }

  isActive.value = false
}

onMounted(async () => {
  await getDataInfo()
})
</script>
